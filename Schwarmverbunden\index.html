<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCHWARMVERBUNDEN</title>
    <link rel="icon" type="image/png" href="assets/favicon.png">
    <link rel="stylesheet" href="styles.css">
</head>
<body>


    <!-- Banner Community -->
    <div class="banner banner-zukunftinsjetzt" id="community">
        <h1 class="mobile-hero-title">SCHWARMVERBUNDEN</h1>
        <h2 class="desktop-subtitle">CO-KREATION FÜR LIGHTWORKER</h2>
    </div>

    <!-- Community Section -->
    <div class="community-section">
        <h1 class="section-main-title">NUR ZUSAMMEN ERGIBT SICH EIN GESAMTBILD</h1>

        <div class="community-content">
            <p><PERSON><PERSON> Lightworker,</p>

            <div class="letter-body">
                <p>die letzten Jahre haben wir unser Innen transformiert. Jetzt ist das Außen dran.<br>
                Wir sind gut vorbereitet, haben unsere Souveränität gefunden und die einzigartige Auf(Gabe) entdeckt, für die wir hier sind.</p>

                <p>Doch etwas lässt uns noch in einer Warteposition verharren: Wir sind noch nicht mit unserem Soultribe verbunden. Jeder hält ein wichtiges Puzzlestück in der Hand, aber wir können das Gesamtbild erst in Klarheit sehen, wenn wir das Puzzle gemeinsam zusammensetzen.</p>

                <p>Ich hatte eine "Zukunftsvision", in der etwas Magisches passiert, sobald wir zusammenkommen: Wir werden zu einem Schwarm. Wie die Zellen im Körper nehmen wir unseren wahren Platz im kollektiven Organismus ein und agieren aus unserer jeweiligen Geniezone heraus. Intuitiv miteinander verbunden, lassen wir uns durch Impulse unserer gemeinsamen Schwarmintelligenz tragen. Wir fließen in Leichtigkeit durch die Welt und kreieren so viel Wertschöpfung, dass Fülle für alle da ist.</p>

                <p>Wenn wir unsere Fähigkeiten kombinieren, vergrößert sich unser Möglichkeitspotenzial ins Unendliche. Ich lade euch von ganzem Herzen zur gemeinsamen Co-Kreation ein!<br> In Liebe, Jana.</p>
            </div>
            <div class="letter-action">
                <a href="https://t.me/+GQrwl1SAiuw2NmUy" class="einheitsbutton" target="_blank">Werde Teil des Schwarms</a>
            </div>
        </div>
    </div>

    <!-- Events Image Section -->
    <div class="events-image-section" id="events">
        <img src="assets/bilder/zusammenhalt.png" alt="Schwarmkreiert Event" class="events-horizontal-image">
        <h1 class="section-main-title events-title">ERLEBE CO-KREATION LIVE IN DER EVENT-WOCHE "SCHWARMKREIERT"!</h1>
    </div>

    <!-- Events Content Section -->
    <div class="events-content-section">
         <h3>Was du in dieser Woche erleben kannst</h3>
            <ul class="possibilities-list">
                <li>Bringe gemeinsam mit deinem Soultribe deine größten Träume in die Wirklichkeit.</li>
                <li>Verbinde dich mit den Fähigkeiten und Gaben der anderen Lightworker.</li>
                <li>Lass dein Licht erstrahlen und erlebe, wie du aufblühst, wenn deine Gaben in der Gruppe erkannt und wertgeschätzt werden.</li>
                <li>Lass dich fallen in einen sicheren Raum, in dem du Fehler machen und unperfekt sein darfst.</li>
                </ul>

         <h3>Wie wir zusammenkommen</h3>
         <div class="slideshow-container">
             <div class="slideshow-wrapper">
                 <!-- Interne Navigation -->
                 <button class="slide-nav-left" id="prevBtnInternal">‹</button>
                 <button class="slide-nav-right" id="nextBtnInternal">›</button>

                 <!-- Interne Indikatoren -->
                 <div class="slide-indicators-internal">
                     <span class="indicator-internal active" data-slide="0"></span>
                     <span class="indicator-internal" data-slide="1"></span>
                     <span class="indicator-internal" data-slide="2"></span>
                     <span class="indicator-internal" data-slide="3"></span>
                     <span class="indicator-internal" data-slide="4"></span>
                     <span class="indicator-internal" data-slide="5"></span>
                     <span class="indicator-internal" data-slide="6"></span>
                 </div>

                 <div class="slides-track" id="slidesTrack">
                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Wir bauen ein Puzzle aus unserem Licht.</strong> Aus all unseren Gaben, Essenzen und Fähigkeiten ergibt sich eine ganz spezifische Fähigkeiten-Kombination, die es so nur gibt, weil genau diese Menschen an einem Ort zusammengekommen sind. Wir gehen in einen co-kreativen Flow und schauen, was sich durch uns ins Leben bringen möchte.</p>
                         </div>
                     </div>

                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Du bringst Licht und Schatten mit.</strong> Und vielleicht hast du genau im richtigen Moment das passende Licht für den Schatten einer anderen Person, und umgekehrt. Alles darf da sein, alles darf sich zeigen.</p>
                         </div>
                     </div>

                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Die Veranstaltung ist ergebnisoffen.</strong> Wir folgen einfach dem, was sich ergibt. Das können alle erdenklichen Formen von Ideen sein, Projekte, Angebote, gemeinsame Unternehmen, die sich gründen. Es können aber auch einfach neue Perspektiven oder gelöste Gruppendynamiken sein, neue Erfahrungen oder neue Soul Friends. Alles, was dazu beiträgt, eine neue und schöne Zukunft zu gestalten und das Paradigma unserer Welt zum Positiven zu verändern.</p>
                         </div>
                     </div>

                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Wir begegnen uns mit einer wohlwollenden Grundhaltung.</strong> Wir gehen davon aus, dass jeder etwas Wichtiges beizutragen hat. Wir entwickeln integrale Lösungen, die all unsere Perspektiven verstehen und berücksichtigen. Wir erlauben uns, fühlende Wesen mit Schatten und Unsicherheiten zu sein. Wir erschaffen einen Raum, in dem wir endlich aufatmen und uns fließen lassen können.</p>
                         </div>
                     </div>

                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Unsere gemeinsame Ausrichtung</strong> liegt darin, den inneren Ruf und das innere Licht von allen kennenzulernen und etwas Gemeinsames zu kreieren. Dabei ist die Veranstaltung nicht nach einem strengen Programm getaktet, sondern folgt nur einem losen Rahmen, der durch einen Sharing Circle morgens und abends die gemeinsame Ausrichtung im Bewusstsein hält. Alles andere ergibt sich im gemeinsamen Flow.</p>
                         </div>
                     </div>

                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Die Veranstaltung findet regelmäßig statt.</strong> Du kannst teilnehmen so oft du möchtest. Durch die immer neue Kombination an Menschen und ihren Fähigkeiten ist jede Veranstaltung einzigartig. Über die Zeit lernen wir gemeinsam, immer tiefer in den Flow zu kommen. Vielleicht wächst die Gruppengröße und wir entwickeln eine immer größere gemeinsame Stärke.</p>
                         </div>
                     </div>

                     <div class="slide">
                         <div class="highlight-box">
                             <p><strong>Die Veranstaltung findet bei jeder Anmeldezahl statt.</strong> Eine Gruppe aus 3 Personen kann eine genauso spannende Co-Kreation hervorbringen wie eine Gruppe aus 4.000 Personen.</p>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
        <h3>Wöchentlicher Ablauf</h3>
        <div class="accordion-container">
            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 1 —</span>
                    <span class="day-focus">"Den Ruf erkunden"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Wir gehen in einen Deep Dive, um unsere Wünsche, Bedürfnisse und Lebensvisionen in der Tiefe zu ergründen. Wir finden heraus, was uns ruft, womit wir uns auf dieser Welt gerne beschäftigen möchten. Ziel ist es, alle in der Gruppe und ihre Motivationen wirklich zu verstehen.</p>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 2 —</span>
                    <span class="day-focus">"Das Licht erkunden"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Im heutigen Deep Dive ergründen wir all unser Licht, unsere Gaben, Fähigkeiten und höchsten Essenzen unseres Daseins. Wir finden heraus, wer von uns welches Licht zu diesem Event mitgebracht hat.</p>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 3 —</span>
                    <span class="day-focus">"Das gemeinsame Puzzle"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Wir suchen nach Gemeinsamkeiten in unseren Visionen, nach Unterschieden in unserem Licht und finden heraus, wie wir uns gegenseitig mit unseren Fähigkeiten ergänzen können, ausgerichtet auf ein (oder mehrere) gemeinsame Ziele, die wir für diese Woche für uns aufstellen.</p>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 4 —</span>
                    <span class="day-focus">"Gruppendynamiken und Hürden lösen"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Wir haben uns nun schon besser gelernt und vielleicht sind uns Situationen aufgefallen, wo wir nicht so wirklich in einem gemeinsamen Flow waren. Genau diese Momente schauen wir uns an und versuchen Wege zu finden, Hürden und (unterschwellige) Beziehungsdynamiken wieder in den Fluss zu bringen.</p>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 5 —</span>
                    <span class="day-focus">"Zum Schwarm werden"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Heute bringen wir unsere entwickelten Ideen und Ziele in die Wirklichkeit. Wir greifen auf die Schwarmintelligenz unserer gesamten Gruppe zu und werden zu einem kollektiven Organismus, bei dem jede Zelle in der eigenen Geniezone agiert.</p>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 6 —</span>
                    <span class="day-focus">"Im Flow"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Der heutige Tag ist thematisch offengehalten, um flexibel auf die Anforderungen unseres entstandenen Schwarm-Kollektivs einzugehen. Vielleicht brauchen wir mehrere Tage im Flow, um ein kreatives Projekt auszuarbeiten. Oder vielleicht haben sich mehrere Untergruppen gebildet und es wird Zeit, dass wir uns die Projekte gegenseitig vorstellen und wieder zu einem großen Ganzen zusammenfügen.</p>
                </div>
            </div>

            <div class="accordion-item">
                <div class="accordion-header" onclick="toggleAccordion(this)">
                    <span class="day-number">— Tag 7 —</span>
                    <span class="day-focus">"Abschluss und Reflexion"</span>
                    <span class="accordion-icon">+</span>
                </div>
                <div class="accordion-content">
                    <p>Alles, was noch gebraucht wird, um unsere Projekte zu einem guten Abschluss zu bringen oder ggf. ein Weiterführen in der Zukunft zu planen, kann heute passieren. Wir reflektieren gemeinsam den Prozess und was wir in den letzten Tagen gefühlt, gelernt und erfahren haben. Wir sprechen darüber, wie uns die Co-Kreation gelungen ist und was wir beim nächsten Mal anders organisieren würde.</p>
                </div>
            </div>
        </div>


        <h3>Praktische Informationen</h3>
        <div class="facts-box">
            <ul class="facts-list">
                <li><strong>Programm:</strong> kein Programm, nur Flow innerhalb einer gemeinsamen Ausrichtung</li>
                <li><strong>Dauer:</strong> 1 Woche</li>
                <li><strong>Verpflegung:</strong> Vollverpflegung vegetarisch/vegan</li>
                <li><strong>Voraussetzung:</strong> Wohlwollende innere Grundhaltung, Offenheit gegenüber Schattenthemen, Bereitschaft Gruppendynamiken und eigene Projektionen zu durchschauen</li>
                <li><strong>Zeitraum:</strong> noch nicht festgelegt</li>
                <li><strong>Ort:</strong> noch nicht festgelegt</li>
                <li><strong>Preis:</strong> noch nicht festgelegt</li>
            </ul>
        </div>
<p>Die Organisation dieser Veranstaltungsreihe ist gerade noch in Planung. Du hast zufällig deine Geniezone im Bereich Eventmanagement, Retreatküche oder Betriebswirtschaft gefunden und gehst mit meiner Vision in Resonanz? Dann melde dich gerne bei mir!</p>
<p>Ich freue mich sehr auf das Event mit euch!</p>
<span class="einheitsbutton inactive">Coming soon</span>
    </div>
    
    <!-- Zwischenmenschlichkeit Image Section -->
    <div class="zwischenmenschlichkeit-image-section" id="zwischenmenschlichkeit">
        <img src="assets/bilder/zwischenmenschlichkeit.png" alt="Zwischenmenschlichkeit" class="zwischenmenschlichkeit-horizontal-image">
        <h1 class="section-main-title zwischenmenschlichkeit-title">FUNDAMENT UNSERER VERBINDUNG IST EINE WOHLWOLLENDE INNERE GRUNDHALTUNG</h1>
    </div>

    <!-- Zwischenmenschlichkeit Content Section -->
    <div class="zwischenmenschlichkeit-content-section">
        <p>Ich habe die letzten Jahre daran geschmiedet, was es braucht, um ein wirklich schönes Zusammensein zu ermöglichen. Dabei habe ich festgestellt, dass es vor allem darum geht, dass sich alle Beteiligten auf eine wohlwollende Grundhaltung einigen. Dann können auch mal Probleme auftreten oder Fehler passieren, die Beziehung oder Gruppe wächst daran immer nur weiter zusammen und wird resilient. Diese innere Grundhaltung als Basis für eine neue Form der Zwischenmenschlichkeit möchte ich dir gerne vorstellen:</p>

        <h3>Innere Grundhaltung</h3>
        <div class="flip-grid">
            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Wohlwollende Absicht</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns zusammenkommen in der Absicht, wirklich das Beste füreinander zu wollen.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Allumfassende Annahme</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns einen Raum kreieren, in dem so viel bedingungslose Annahme existiert, dass wir uns mit all unseren Facetten zeigen können.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Positives Menschenbild</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns die Absichten und Bedürfnisse von Menschen so tief erforschen, dass wir merken, dass sie im Kern gut sind.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Kompatibilität</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns prüfen, ob unsere Werte, Visionen und Lebensausrichtung zueinander passen.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Wirklich verstehen wollen</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns entscheiden, uns selbst und die anderen wirklich in der Tiefe verstehen, fühlen, hören und sehen zu wollen.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Projektionen durchschauen</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns Triggerdynamiken gemeinsam durchstehen, unsere Projektionen durchschauen und unsere Anteile zu uns zurücknehmen.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>Integrale Lösungen finden</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns umfassende und ganzheitliche "All-Win"-Lösungen entwickeln, indem wir unterschiedliche Perspektiven umarmen.</p>
                    </div>
                </div>
            </div>

            <div class="flip-card-compact">
                <div class="flip-card-inner">
                    <div class="flip-card-front">
                        <h4>An Heraus-<br>forderungen wachsen</h4>
                    </div>
                    <div class="flip-card-back">
                        <p>Lass uns zwischenmenschliche Schwierigkeiten als Anlass nehmen, gemeinsam zu heilen und zu wachsen.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Community Button nach Flipcards -->
        <div class="community-button-section">
            <a href="https://t.me/+GQrwl1SAiuw2NmUy" class="einheitsbutton" target="_blank">Werde Teil des Schwarms</a>
        </div>
    </div>

    <!-- Letter Closing Banner/Pre-Footer -->
    <div class="letter-closing-banner">
        <div class="closing-overlay">
            <div class="closing-content">
                <img src="assets/bilder/profilbild.jpg" alt="Jana Sophie Breitmar" class="closing-profile-image">
                <div class="closing-text">
                    <p class="letter-signature">In Liebe, Jana</p>
                    <h3>Verbinde dich mit mir</h3>
                    <div class="social-links">
                        <a href="https://instagram.com/schwarmverbunden" target="_blank" class="einheitsbutton instagram">Instagram</a>
                        <a href="https://open.spotify.com/intl-de/artist/3UtRmfHSN1nHEx3a0jWgwi?si=Qrqnbh4aReOK4flnzzpM6A" target="_blank" class="einheitsbutton spotify">Spotify</a>
                        <a href="https://t.me/+GQrwl1SAiuw2NmUy" target="_blank" class="einheitsbutton telegram">Telegram</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    


    <script src="header-and-footer.js"></script>
    <script src="script.js"></script>
    <script>
        // Modern Slideshow mit Touch-Gesten
        class ModernSlideshow {
            constructor() {
                this.currentSlide = 0;
                this.totalSlides = 7;
                this.slidesTrack = document.getElementById('slidesTrack');
                this.indicators = document.querySelectorAll('.indicator');
                this.prevBtn = document.getElementById('prevBtn');
                this.nextBtn = document.getElementById('nextBtn');

                // Touch-Variablen
                this.startX = 0;
                this.currentX = 0;
                this.isDragging = false;
                this.threshold = 50; // Mindest-Swipe-Distanz

                this.init();
            }

            init() {
                // Button Event Listeners
                this.prevBtn.addEventListener('click', () => this.prevSlide());
                this.nextBtn.addEventListener('click', () => this.nextSlide());

                // Indicator Event Listeners
                this.indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => this.goToSlide(index));
                });

                // Touch Event Listeners
                this.slidesTrack.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
                this.slidesTrack.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: true });
                this.slidesTrack.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

                // Mouse Event Listeners für Desktop
                this.slidesTrack.addEventListener('mousedown', (e) => this.handleMouseStart(e));
                this.slidesTrack.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.slidesTrack.addEventListener('mouseup', (e) => this.handleMouseEnd(e));
                this.slidesTrack.addEventListener('mouseleave', (e) => this.handleMouseEnd(e));

                // Keyboard Navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') this.prevSlide();
                    if (e.key === 'ArrowRight') this.nextSlide();
                });

                this.updateSlideshow();
            }

            // Touch Events
            handleTouchStart(e) {
                this.startX = e.touches[0].clientX;
                this.isDragging = true;
            }

            handleTouchMove(e) {
                if (!this.isDragging) return;
                this.currentX = e.touches[0].clientX;
            }

            handleTouchEnd(e) {
                if (!this.isDragging) return;
                this.isDragging = false;

                const diffX = this.startX - this.currentX;

                if (Math.abs(diffX) > this.threshold) {
                    if (diffX > 0) {
                        this.nextSlide();
                    } else {
                        this.prevSlide();
                    }
                }
            }

            // Mouse Events (für Desktop)
            handleMouseStart(e) {
                this.startX = e.clientX;
                this.isDragging = true;
                this.slidesTrack.style.cursor = 'grabbing';
            }

            handleMouseMove(e) {
                if (!this.isDragging) return;
                this.currentX = e.clientX;
            }

            handleMouseEnd(e) {
                if (!this.isDragging) return;
                this.isDragging = false;
                this.slidesTrack.style.cursor = 'grab';

                const diffX = this.startX - this.currentX;

                if (Math.abs(diffX) > this.threshold) {
                    if (diffX > 0) {
                        this.nextSlide();
                    } else {
                        this.prevSlide();
                    }
                }
            }

            nextSlide() {
                this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
                this.updateSlideshow();
            }

            prevSlide() {
                this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
                this.updateSlideshow();
            }

            goToSlide(index) {
                this.currentSlide = index;
                this.updateSlideshow();
            }

            updateSlideshow() {
                // Slides bewegen
                const translateX = -this.currentSlide * (100 / this.totalSlides);
                this.slidesTrack.style.transform = `translateX(${translateX}%)`;

                // Indicators aktualisieren
                this.indicators.forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === this.currentSlide);
                });
            }
        }

        // Slideshow initialisieren wenn DOM geladen ist
        document.addEventListener('DOMContentLoaded', () => {
            new ModernSlideshow();
        });
    </script>
</body>
</html>